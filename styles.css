* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: radial-gradient(ellipse at center, #0f0f23 0%, #000000 70%),
                linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);
    color: white;
    overflow: hidden;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

/* Animated starfield background */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, #eee, transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
        radial-gradient(1px 1px at 90px 40px, #fff, transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
        radial-gradient(2px 2px at 160px 30px, #ddd, transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: sparkle 20s linear infinite;
    pointer-events: none;
    z-index: -1;
}

/* Additional star layers for depth */
body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(1px 1px at 50px 50px, rgba(255,255,255,0.4), transparent),
        radial-gradient(1px 1px at 100px 100px, rgba(255,255,255,0.3), transparent),
        radial-gradient(1px 1px at 150px 150px, rgba(255,255,255,0.5), transparent),
        radial-gradient(2px 2px at 200px 200px, rgba(255,255,255,0.2), transparent);
    background-repeat: repeat;
    background-size: 300px 300px;
    animation: sparkle 30s linear infinite reverse;
    pointer-events: none;
    z-index: -1;
}

@keyframes sparkle {
    from { transform: translateX(0); }
    to { transform: translateX(-200px); }
}

.container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.menu {
    text-align: center;
    background: linear-gradient(145deg, rgba(15, 15, 35, 0.9), rgba(0, 0, 0, 0.95));
    padding: 40px;
    border-radius: 20px;
    border: 2px solid rgba(0, 255, 136, 0.3);
    box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.7),
        0 0 50px rgba(0, 100, 255, 0.2),
        inset 0 0 30px rgba(0, 255, 136, 0.1);
    backdrop-filter: blur(15px);
}

.menu h1 {
    font-size: 3rem;
    margin-bottom: 20px;
    color: #00ff88;
    text-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
    letter-spacing: 3px;
}

.menu p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    color: #ccc;
}

.btn {
    background: linear-gradient(45deg, #00ff88, #00cc6a);
    border: none;
    padding: 15px 30px;
    font-size: 1.2rem;
    color: white;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 255, 136, 0.3);
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: bold;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 255, 136, 0.5);
    background: linear-gradient(45deg, #00cc6a, #00ff88);
}

.btn:active {
    transform: translateY(0);
}

/* Difficulty Selection Styles */
.difficulty-selection {
    margin: 30px 0;
}

.difficulty-selection h3 {
    color: #00ff88;
    font-size: 1.5rem;
    margin-bottom: 20px;
    text-shadow: 0 0 10px rgba(0, 255, 136, 0.3);
}

.difficulty-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.difficulty-btn {
    background: linear-gradient(145deg, rgba(15, 15, 35, 0.8), rgba(0, 0, 0, 0.9));
    border: 2px solid rgba(0, 255, 136, 0.3);
    padding: 15px 20px;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #ccc;
    text-align: center;
    min-width: 120px;
    backdrop-filter: blur(10px);
}

.difficulty-btn:hover {
    border-color: rgba(0, 255, 136, 0.6);
    background: linear-gradient(145deg, rgba(0, 255, 136, 0.1), rgba(0, 0, 0, 0.9));
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 255, 136, 0.2);
}

.difficulty-btn.active {
    border-color: #00ff88;
    background: linear-gradient(145deg, rgba(0, 255, 136, 0.2), rgba(0, 0, 0, 0.9));
    color: #00ff88;
    box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
}

.difficulty-title {
    display: block;
    font-weight: bold;
    font-size: 1rem;
    margin-bottom: 5px;
}

.difficulty-desc {
    display: block;
    font-size: 0.8rem;
    opacity: 0.8;
}

.game-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.game-info {
    display: flex;
    justify-content: space-between;
    width: 800px;
    max-width: 90vw;
    padding: 0 40px;
    font-size: 1rem;
    color: #ccc;
}

.difficulty-display, .winning-score-display {
    text-align: center;
}

.difficulty-display span:last-child,
.winning-score-display span:nth-child(2) {
    color: #00ff88;
    font-weight: bold;
    text-shadow: 0 0 5px rgba(0, 255, 136, 0.3);
}

.score-board {
    display: flex;
    justify-content: space-between;
    width: 800px;
    max-width: 90vw;
    padding: 0 40px;
}

.score {
    text-align: center;
    font-size: 1.5rem;
    font-weight: bold;
}

.score span:first-child {
    display: block;
    color: #ccc;
    font-size: 1rem;
    margin-bottom: 5px;
}

.score span:last-child {
    font-size: 2.5rem;
    color: #00ff88;
    text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
}

#gameCanvas {
    border: 3px solid #00ff88;
    border-radius: 10px;
    background: radial-gradient(ellipse at center, rgba(15, 15, 35, 0.9) 0%, rgba(0, 0, 0, 0.95) 70%);
    box-shadow:
        0 0 30px rgba(0, 255, 136, 0.3),
        inset 0 0 50px rgba(0, 0, 0, 0.5),
        0 0 100px rgba(0, 100, 255, 0.1);
    max-width: 90vw;
    max-height: 60vh;
    position: relative;
}

.controls {
    text-align: center;
    color: #ccc;
    font-size: 0.9rem;
}

.final-score {
    font-size: 1.5rem;
    margin: 20px 0;
    color: #00ff88;
    font-weight: bold;
}

.hidden {
    display: none !important;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .menu h1 {
        font-size: 2rem;
    }

    .menu p {
        font-size: 1rem;
    }

    .btn {
        padding: 12px 25px;
        font-size: 1rem;
    }

    .difficulty-selection h3 {
        font-size: 1.3rem;
    }

    .difficulty-buttons {
        flex-direction: column;
        align-items: center;
    }

    .difficulty-btn {
        min-width: 200px;
        padding: 12px 15px;
    }

    .game-info {
        width: 100%;
        padding: 0 20px;
        font-size: 0.9rem;
    }

    .score-board {
        width: 100%;
        padding: 0 20px;
    }
    
    .score span:last-child {
        font-size: 2rem;
    }
    
    #gameCanvas {
        width: 90vw;
        height: auto;
    }
}

@media (max-width: 480px) {
    .menu {
        padding: 30px 20px;
    }

    .menu h1 {
        font-size: 1.8rem;
    }

    .game-info {
        flex-direction: column;
        gap: 10px;
        font-size: 0.8rem;
    }

    .score {
        font-size: 1.2rem;
    }

    .score span:last-child {
        font-size: 1.8rem;
    }
}
