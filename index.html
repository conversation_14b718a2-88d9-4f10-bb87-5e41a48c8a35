<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ping Pong Game</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <!-- Start Menu -->
        <div id="startMenu" class="menu">
            <h1>PING PONG</h1>
            <p>Use Arrow Keys to control your paddle</p>

            <!-- Difficulty Selection -->
            <div class="difficulty-selection">
                <h3>Choose Difficulty</h3>
                <div class="difficulty-buttons">
                    <button id="beginnerBtn" class="difficulty-btn active" data-difficulty="beginner">
                        <span class="difficulty-title">Beginner</span>
                        <span class="difficulty-desc">Easy pace, forgiving AI</span>
                    </button>
                    <button id="moderateBtn" class="difficulty-btn" data-difficulty="moderate">
                        <span class="difficulty-title">Moderate</span>
                        <span class="difficulty-desc">Balanced challenge</span>
                    </button>
                    <button id="challengerBtn" class="difficulty-btn" data-difficulty="challenger">
                        <span class="difficulty-title">Challenger</span>
                        <span class="difficulty-desc">Fast & intense</span>
                    </button>
                </div>
            </div>

            <button id="startBtn" class="btn">Start Game</button>
        </div>

        <!-- Game Area -->
        <div id="gameArea" class="game-area hidden">
            <div class="game-info">
                <div class="difficulty-display">
                    <span>Difficulty: </span>
                    <span id="currentDifficulty">Beginner</span>
                </div>
                <div class="winning-score-display">
                    <span>First to </span>
                    <span id="currentWinningScore">10</span>
                    <span> wins!</span>
                </div>
            </div>
            <div class="score-board">
                <div class="score">
                    <span>Player</span>
                    <span id="playerScore">0</span>
                </div>
                <div class="score">
                    <span>AI</span>
                    <span id="aiScore">0</span>
                </div>
            </div>
            <canvas id="gameCanvas" width="800" height="400"></canvas>
            <div class="game-controls">
                <button id="pauseBtn" class="control-btn">⏸️ Pause</button>
            </div>
            <div class="controls">
                <p>Use ↑ ↓ Arrow Keys to move your paddle • Press SPACE to pause</p>
            </div>
        </div>

        <!-- Pause Overlay -->
        <div id="pauseOverlay" class="pause-overlay hidden">
            <div class="pause-menu">
                <h2>GAME PAUSED</h2>
                <p>Press SPACE or click Resume to continue</p>
                <div class="pause-buttons">
                    <button id="resumeBtn" class="btn">▶️ Resume</button>
                    <button id="quitBtn" class="btn secondary">🏠 Main Menu</button>
                </div>
            </div>
        </div>

        <!-- Game Over Menu -->
        <div id="gameOverMenu" class="menu hidden">
            <h1 id="gameOverTitle">GAME OVER</h1>
            <p id="gameOverMessage"></p>
            <div class="final-score">
                <span>Final Score: </span>
                <span id="finalPlayerScore">0</span>
                <span> - </span>
                <span id="finalAiScore">0</span>
            </div>
            <button id="playAgainBtn" class="btn">Play Again</button>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
