// Game state management
const GameState = {
    MENU: 'menu',
    PLAYING: 'playing',
    PAUSED: 'paused',
    GAME_OVER: 'gameOver'
};

// Difficulty configurations
const DifficultySettings = {
    beginner: {
        name: 'Beginner',
        ballSpeed: 4,
        playerPaddleSpeed: 8,
        aiPaddleSpeed: 4,
        aiReactionDelay: 50, // Higher delay = easier AI
        winningScore: 10
    },
    moderate: {
        name: 'Moderate',
        ballSpeed: 6,
        playerPaddleSpeed: 8,
        aiPaddleSpeed: 6,
        aiReactionDelay: 35,
        winningScore: 10
    },
    challenger: {
        name: 'Challenger',
        ballSpeed: 8,
        playerPaddleSpeed: 9,
        aiPaddleSpeed: 8,
        aiReactionDelay: 20, // Lower delay = harder AI
        winningScore: 15
    }
};

class PingPongGame {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.gameState = GameState.MENU;

        // Current difficulty setting
        this.currentDifficulty = 'beginner';
        this.difficultySettings = DifficultySettings[this.currentDifficulty];

        // Game objects
        this.ball = {
            x: 400,
            y: 200,
            radius: 8,
            velocityX: 5,
            velocityY: 3,
            speed: this.difficultySettings.ballSpeed
        };

        this.playerPaddle = {
            x: 20,
            y: 150,
            width: 15,
            height: 100,
            speed: this.difficultySettings.playerPaddleSpeed
        };

        this.aiPaddle = {
            x: 765,
            y: 150,
            width: 15,
            height: 100,
            speed: this.difficultySettings.aiPaddleSpeed
        };

        this.score = {
            player: 0,
            ai: 0
        };

        this.keys = {};
        this.winningScore = this.difficultySettings.winningScore;

        // Space background stars
        this.stars = [];
        this.initializeStars();

        this.initializeEventListeners();
        this.initializeAudio();
    }
    
    initializeEventListeners() {
        // Menu buttons
        document.getElementById('startBtn').addEventListener('click', () => this.startGame());
        document.getElementById('playAgainBtn').addEventListener('click', () => this.resetGame());

        // Difficulty selection buttons
        document.getElementById('beginnerBtn').addEventListener('click', () => this.setDifficulty('beginner'));
        document.getElementById('moderateBtn').addEventListener('click', () => this.setDifficulty('moderate'));
        document.getElementById('challengerBtn').addEventListener('click', () => this.setDifficulty('challenger'));

        // Pause/Resume buttons
        document.getElementById('pauseBtn').addEventListener('click', () => this.togglePause());
        document.getElementById('resumeBtn').addEventListener('click', () => this.resumeGame());
        document.getElementById('quitBtn').addEventListener('click', () => this.quitToMenu());

        // Keyboard controls
        document.addEventListener('keydown', (e) => {
            this.keys[e.key] = true;

            // Handle pause/resume with spacebar
            if (e.code === 'Space') {
                e.preventDefault(); // Prevent page scroll
                if (this.gameState === GameState.PLAYING || this.gameState === GameState.PAUSED) {
                    this.togglePause();
                }
            }
        });

        document.addEventListener('keyup', (e) => {
            this.keys[e.key] = false;
        });
    }
    
    initializeStars() {
        // Create background stars for space effect
        for (let i = 0; i < 100; i++) {
            this.stars.push({
                x: Math.random() * this.canvas.width,
                y: Math.random() * this.canvas.height,
                size: Math.random() * 2 + 0.5,
                speed: Math.random() * 0.5 + 0.1,
                opacity: Math.random() * 0.8 + 0.2
            });
        }
    }

    initializeAudio() {
        // Create audio context for sound effects
        this.audioContext = null;
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext || null)();
        } catch (e) {
            console.log('Audio not supported');
        }
    }
    
    playSound(frequency, duration, type = 'sine') {
        if (!this.audioContext) return;

        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        oscillator.frequency.value = frequency;
        oscillator.type = type;

        gainNode.gain.setValueAtTime(0.3, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + duration);

        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + duration);
    }

    setDifficulty(difficulty) {
        this.currentDifficulty = difficulty;
        this.difficultySettings = DifficultySettings[difficulty];

        // Update difficulty button states
        document.querySelectorAll('.difficulty-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.getElementById(difficulty + 'Btn').classList.add('active');

        // Update game settings
        this.ball.speed = this.difficultySettings.ballSpeed;
        this.playerPaddle.speed = this.difficultySettings.playerPaddleSpeed;
        this.aiPaddle.speed = this.difficultySettings.aiPaddleSpeed;
        this.winningScore = this.difficultySettings.winningScore;

        // Update game info display
        this.updateGameInfoDisplay();
    }

    updateGameInfoDisplay() {
        document.getElementById('currentDifficulty').textContent = this.difficultySettings.name;
        document.getElementById('currentWinningScore').textContent = this.winningScore;
    }

    togglePause() {
        if (this.gameState === GameState.PLAYING) {
            this.pauseGame();
        } else if (this.gameState === GameState.PAUSED) {
            this.resumeGame();
        }
    }

    pauseGame() {
        this.gameState = GameState.PAUSED;
        document.getElementById('pauseOverlay').classList.remove('hidden');
        document.getElementById('pauseBtn').textContent = '▶️ Resume';
        this.playSound(400, 0.1); // Pause sound
    }

    resumeGame() {
        this.gameState = GameState.PLAYING;
        document.getElementById('pauseOverlay').classList.add('hidden');
        document.getElementById('pauseBtn').textContent = '⏸️ Pause';
        this.playSound(500, 0.1); // Resume sound
        this.gameLoop(); // Restart the game loop
    }

    quitToMenu() {
        this.gameState = GameState.MENU;
        document.getElementById('pauseOverlay').classList.add('hidden');
        document.getElementById('gameArea').classList.add('hidden');
        document.getElementById('startMenu').classList.remove('hidden');
        document.getElementById('pauseBtn').textContent = '⏸️ Pause';
    }
    
    startGame() {
        this.gameState = GameState.PLAYING;
        this.showGameArea();
        this.updateGameInfoDisplay();
        this.resetBall();
        this.gameLoop();
    }
    
    resetGame() {
        this.score.player = 0;
        this.score.ai = 0;
        this.updateScoreDisplay();
        this.startGame();
    }
    
    showGameArea() {
        document.getElementById('startMenu').classList.add('hidden');
        document.getElementById('gameOverMenu').classList.add('hidden');
        document.getElementById('gameArea').classList.remove('hidden');
    }
    
    showGameOver() {
        document.getElementById('gameArea').classList.add('hidden');
        document.getElementById('gameOverMenu').classList.remove('hidden');

        const winner = this.score.player >= this.winningScore ? 'Player' : 'AI';
        document.getElementById('gameOverTitle').textContent =
            winner === 'Player' ? 'YOU WIN!' : 'AI WINS!';

        const difficultyName = this.difficultySettings.name;
        const message = winner === 'Player'
            ? `Congratulations! You beat the AI on ${difficultyName} difficulty!`
            : `Better luck next time! Try a different difficulty level.`;
        document.getElementById('gameOverMessage').textContent = message;

        document.getElementById('finalPlayerScore').textContent = this.score.player;
        document.getElementById('finalAiScore').textContent = this.score.ai;
    }
    
    resetBall() {
        this.ball.x = this.canvas.width / 2;
        this.ball.y = this.canvas.height / 2;
        // Use current difficulty ball speed
        this.ball.speed = this.difficultySettings.ballSpeed;
        this.ball.velocityX = (Math.random() > 0.5 ? 1 : -1) * this.ball.speed;
        this.ball.velocityY = (Math.random() - 0.5) * this.ball.speed;
    }
    
    updateScoreDisplay() {
        document.getElementById('playerScore').textContent = this.score.player;
        document.getElementById('aiScore').textContent = this.score.ai;
    }
    
    update() {
        if (this.gameState !== GameState.PLAYING) return;

        // Update player paddle
        this.updatePlayerPaddle();

        // Update AI paddle
        this.updateAIPaddle();

        // Update ball
        this.updateBall();

        // Check for scoring
        this.checkScoring();

        // Check for game over
        this.checkGameOver();
    }
    
    updatePlayerPaddle() {
        if (this.keys['ArrowUp'] && this.playerPaddle.y > 0) {
            this.playerPaddle.y -= this.playerPaddle.speed;
        }
        if (this.keys['ArrowDown'] && this.playerPaddle.y < this.canvas.height - this.playerPaddle.height) {
            this.playerPaddle.y += this.playerPaddle.speed;
        }
    }
    
    updateAIPaddle() {
        const paddleCenter = this.aiPaddle.y + this.aiPaddle.height / 2;
        const ballY = this.ball.y;

        // Apply difficulty-based reaction delay
        const reactionThreshold = this.difficultySettings.aiReactionDelay;

        if (paddleCenter < ballY - reactionThreshold) {
            this.aiPaddle.y += this.aiPaddle.speed;
        } else if (paddleCenter > ballY + reactionThreshold) {
            this.aiPaddle.y -= this.aiPaddle.speed;
        }

        // Keep AI paddle within bounds
        this.aiPaddle.y = Math.max(0, Math.min(this.canvas.height - this.aiPaddle.height, this.aiPaddle.y));
    }

    updateBall() {
        this.ball.x += this.ball.velocityX;
        this.ball.y += this.ball.velocityY;

        // Ball collision with top and bottom walls
        if (this.ball.y - this.ball.radius <= 0 || this.ball.y + this.ball.radius >= this.canvas.height) {
            this.ball.velocityY = -this.ball.velocityY;
            this.playSound(300, 0.1);
        }

        // Ball collision with paddles
        this.checkPaddleCollision();
    }

    checkPaddleCollision() {
        // Player paddle collision
        if (this.ball.x - this.ball.radius <= this.playerPaddle.x + this.playerPaddle.width &&
            this.ball.x + this.ball.radius >= this.playerPaddle.x &&
            this.ball.y >= this.playerPaddle.y &&
            this.ball.y <= this.playerPaddle.y + this.playerPaddle.height) {

            this.ball.velocityX = -this.ball.velocityX;
            this.ball.x = this.playerPaddle.x + this.playerPaddle.width + this.ball.radius;

            // Add some spin based on where the ball hits the paddle
            const hitPos = (this.ball.y - this.playerPaddle.y) / this.playerPaddle.height;
            this.ball.velocityY = (hitPos - 0.5) * 8;

            this.playSound(440, 0.1);
        }

        // AI paddle collision
        if (this.ball.x + this.ball.radius >= this.aiPaddle.x &&
            this.ball.x - this.ball.radius <= this.aiPaddle.x + this.aiPaddle.width &&
            this.ball.y >= this.aiPaddle.y &&
            this.ball.y <= this.aiPaddle.y + this.aiPaddle.height) {

            this.ball.velocityX = -this.ball.velocityX;
            this.ball.x = this.aiPaddle.x - this.ball.radius;

            // Add some spin based on where the ball hits the paddle
            const hitPos = (this.ball.y - this.aiPaddle.y) / this.aiPaddle.height;
            this.ball.velocityY = (hitPos - 0.5) * 8;

            this.playSound(440, 0.1);
        }
    }

    checkScoring() {
        // Player scores (ball goes off right side)
        if (this.ball.x > this.canvas.width) {
            this.score.player++;
            this.updateScoreDisplay();
            this.playSound(600, 0.3);
            this.resetBall();
        }

        // AI scores (ball goes off left side)
        if (this.ball.x < 0) {
            this.score.ai++;
            this.updateScoreDisplay();
            this.playSound(200, 0.3);
            this.resetBall();
        }
    }

    checkGameOver() {
        if (this.score.player >= this.winningScore || this.score.ai >= this.winningScore) {
            this.gameState = GameState.GAME_OVER;
            this.showGameOver();
        }
    }

    updateStars() {
        // Move stars to create parallax effect
        this.stars.forEach(star => {
            star.x -= star.speed;
            if (star.x < 0) {
                star.x = this.canvas.width;
                star.y = Math.random() * this.canvas.height;
            }
        });
    }

    drawStars() {
        this.ctx.save();
        this.stars.forEach(star => {
            this.ctx.globalAlpha = star.opacity;
            this.ctx.fillStyle = '#ffffff';
            this.ctx.beginPath();
            this.ctx.arc(star.x, star.y, star.size, 0, Math.PI * 2);
            this.ctx.fill();
        });
        this.ctx.restore();
    }

    draw() {
        // Clear canvas with space background
        const gradient = this.ctx.createRadialGradient(
            this.canvas.width / 2, this.canvas.height / 2, 0,
            this.canvas.width / 2, this.canvas.height / 2, this.canvas.width / 2
        );
        gradient.addColorStop(0, 'rgba(15, 15, 35, 0.95)');
        gradient.addColorStop(1, 'rgba(0, 0, 0, 0.98)');
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // Update and draw stars
        this.updateStars();
        this.drawStars();

        // Draw center line
        this.ctx.setLineDash([5, 15]);
        this.ctx.beginPath();
        this.ctx.moveTo(this.canvas.width / 2, 0);
        this.ctx.lineTo(this.canvas.width / 2, this.canvas.height);
        this.ctx.strokeStyle = '#00ff88';
        this.ctx.lineWidth = 2;
        this.ctx.stroke();
        this.ctx.setLineDash([]);

        // Draw paddles
        this.drawPaddle(this.playerPaddle);
        this.drawPaddle(this.aiPaddle);

        // Draw ball
        this.drawBall();
    }

    drawPaddle(paddle) {
        this.ctx.fillStyle = '#00ff88';
        this.ctx.fillRect(paddle.x, paddle.y, paddle.width, paddle.height);

        // Add glow effect
        this.ctx.shadowColor = '#00ff88';
        this.ctx.shadowBlur = 10;
        this.ctx.fillRect(paddle.x, paddle.y, paddle.width, paddle.height);
        this.ctx.shadowBlur = 0;
    }

    drawBall() {
        this.ctx.beginPath();
        this.ctx.arc(this.ball.x, this.ball.y, this.ball.radius, 0, Math.PI * 2);
        this.ctx.fillStyle = '#ffffff';
        this.ctx.fill();

        // Add glow effect
        this.ctx.shadowColor = '#ffffff';
        this.ctx.shadowBlur = 15;
        this.ctx.fill();
        this.ctx.shadowBlur = 0;
    }

    gameLoop() {
        if (this.gameState === GameState.PLAYING) {
            this.update();
            this.draw();
            requestAnimationFrame(() => this.gameLoop());
        } else if (this.gameState === GameState.PAUSED) {
            // Only draw when paused, don't update game logic
            this.draw();
            requestAnimationFrame(() => this.gameLoop());
        }
    }
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.game = new PingPongGame();
});
